# 通用AI算法管理平台开发指南

**版本**: 2.1.0 | **更新**: 2025-07-31

## 1. 项目概述

统一AI算法管理平台，提供标准化的算法开发、部署和管理解决方案。

### 当前状态
```
算法服务:
├── renchefei-v2 (人车非检测)     - 端口8002 ✅ 正常
├── wenzhou-face-v2 (人脸识别)   - 端口8003 ✅ 正常
└── accident-classify-v2 (事故分类) - 端口8004 ⚠️ 模型质量问题

管理平台:
├── 前端服务 - 端口3000 ✅ 正常
└── 后端API - 端口8100 ✅ 正常
```

### 项目结构
```
algorithm_platform/
├── algorithms/                      # 算法包目录
│   ├── renchefei/                  # 人车非检测
│   ├── wenzhou_face/               # 人脸识别
│   └── accident_classify/          # 事故分类
├── algorithm-platform-manager/     # 管理平台
│   ├── frontend/                   # Vue 3前端
│   └── src/                        # FastAPI后端
└── data/                           # 测试数据
```

### 技术栈
- **前端**: Vue 3 + Element Plus + Vite
- **后端**: Python 3.11 + FastAPI + uv
- **容器**: Docker + Docker Compose

### 端口分配
| 服务 | 端口 | 说明 |
|------|------|------|
| 算法API | 8002-8099 | 算法容器端口 |
| 管理API | 8100 | 平台后端API |
| 前端 | 3000 | 开发服务器 |

## 2. 开发规范

### 2.1 统一响应格式

所有API必须返回以下结构：
```json
{
  "success": true,
  "error": null,
  "data": { /* 业务数据 */ },
  "metadata": { /* 元数据 */ }
}
```

**错误响应格式**：参考 `ERROR_CODES_STANDARD.md` 中的标准化错误代码。

### 2.2 必需API端点

| 端点 | 方法 | 功能 |
|------|------|------|
| `/api/v1/health` | GET | 健康检查 |
| `/api/v1/info` | GET | 算法信息 |
| `/api/v1/detect` | POST | 核心功能 |
| `/docs` | GET | API文档 |

### 2.3 算法项目结构

```
algorithms/{algorithm_name}/
├── pyproject.toml              # uv项目配置
├── Dockerfile                  # 容器配置
├── src/
│   ├── api_server.py           # FastAPI入口
│   ├── models/unified.py       # 统一响应模型
│   ├── core/algorithm.py       # 算法核心逻辑
│   └── config.py               # 配置管理
├── models/                     # 模型文件
└── tests/                      # 测试脚本
```

### 2.4 实时检测系统

**双重检测方案**：
- **HTTP API**: 5-30FPS，标准检测
- **WebSocket**: 5-60FPS，高频实时检测

**WebSocket端点**: `/api/v1/ws/realtime-detect/{container_id}`

### 2.5 Docker容器化

**必需标签**：
```dockerfile
LABEL algorithm.platform="true"
LABEL algorithm.name="算法名称"
LABEL algorithm.type="目标检测"
LABEL algorithm.version="2.0.0"
```

**Dockerfile模板**：
```dockerfile
FROM python:3.11-slim
WORKDIR /app

RUN pip install uv && \
    apt-get update && apt-get install -y curl && \
    rm -rf /var/lib/apt/lists/*

COPY pyproject.toml uv.lock ./
RUN uv sync --frozen --no-dev

COPY src/ ./src/
COPY models/ ./models/

EXPOSE 8000
HEALTHCHECK CMD curl -f http://localhost:8000/api/v1/health
CMD ["uv", "run", "python", "src/api_server.py"]
```

## 3. 开发流程

### 3.1 环境准备

**系统要求**：
- Python 3.11+
- Docker 20.10+
- uv包管理器

**安装uv**：
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
uv --version
```

### 3.2 新算法开发

**创建项目**：
```bash
mkdir algorithms/my_algorithm
cd algorithms/my_algorithm
uv init --name my_algorithm
uv add fastapi uvicorn pydantic pillow numpy
```

**pyproject.toml配置**：
```toml
[project]
name = "my-algorithm"
version = "2.0.0"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.0.0",
    "pillow>=10.0.0",
    "numpy>=1.24.0"
]
```

**统一响应模型** (`src/models/unified.py`)：
```python
from typing import Optional, Any
from pydantic import BaseModel
from datetime import datetime

class UnifiedResponse(BaseModel):
    success: bool
    error: Optional[dict] = None
    data: Optional[Any] = None
    metadata: dict

def create_success_response(data: Any, metadata: dict = None):
    return UnifiedResponse(
        success=True,
        data=data,
        metadata=metadata or {"timestamp_utc": datetime.utcnow().isoformat()}
    )

def create_error_response(code: str, message: str):
    return UnifiedResponse(
        success=False,
        error={"code": code, "message": message},
        data=None,
        metadata={"timestamp_utc": datetime.utcnow().isoformat()}
    )
```

**FastAPI服务器** (`src/api_server.py`)：
```python
from fastapi import FastAPI, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from PIL import Image
import io

from models.unified import create_success_response, create_error_response
from core.algorithm import MyAlgorithm

app = FastAPI(title="My Algorithm API", version="2.0.0")
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_methods=["*"], allow_headers=["*"])

algorithm = MyAlgorithm()

@app.get("/api/v1/health")
async def health_check():
    return create_success_response(data={"status": "healthy"})

@app.get("/api/v1/info")
async def get_algorithm_info():
    return create_success_response(data={
        "algorithm_name": "我的算法",
        "algorithm_version": "2.0.0",
        "supported_formats": ["jpg", "jpeg", "png"]
    })

@app.post("/api/v1/detect")
async def detect_objects(file: UploadFile = File(...)):
    try:
        if not file.content_type.startswith('image/'):
            return create_error_response("INVALID_FILE_FORMAT", "不支持的文件格式")

        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        results, processing_time = algorithm.process(image)

        return create_success_response(data=results, metadata={"processing_time_ms": processing_time})
    except Exception as e:
        return create_error_response("PROCESSING_ERROR", f"处理失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 3.3 测试与部署

**本地测试**：
```bash
cd algorithms/my_algorithm
uv run python src/api_server.py

# 测试API
curl http://localhost:8000/api/v1/health
curl -X POST "http://localhost:8000/api/v1/detect" -F "file=@test_image.jpg"
```

**容器化**：
```bash
docker build -t my-algorithm:2.0.0 .
docker run -p 8005:8000 my-algorithm:2.0.0
```

## 4. 部署与运维

### 4.1 管理平台启动

```bash
cd algorithm-platform-manager

# 启动后端
uv run python src/main.py

# 启动前端 (新终端)
cd frontend
npm run dev
```

**访问地址**：
- 管理界面: http://localhost:3000
- API文档: http://localhost:8100/docs

### 4.2 算法容器部署

**统一部署**：
```bash
docker-compose -f docker-compose-algorithms.yml up -d
docker ps
curl http://localhost:8002/api/v1/health  # 验证健康状态
```

**增量部署**：
```bash
docker-compose -f docker-compose-algorithms.yml up -d new-algorithm
curl http://localhost:8005/api/v1/health
```

### 4.3 生产环境配置

**Docker Compose示例**：
```yaml
version: '3.8'
services:
  my-algorithm:
    build: .
    ports:
      - "8005:8000"
    volumes:
      - ./models:/app/models:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## 5. 故障排除

### 5.1 常见问题

**容器启动失败**：
```bash
docker logs container_name
netstat -tulpn | grep 8000
docker inspect container_name | grep -A 10 "Labels"
```

**API响应格式错误**：
确保使用统一响应格式，参考 `ERROR_CODES_STANDARD.md`。

**性能优化**：
- 使用ONNX、TensorRT优化模型
- 支持批量推理
- 使用GPU加速

**内存泄漏处理**：
```python
import gc
def process_with_cleanup(image):
    try:
        result = algorithm.process(image)
        return result
    finally:
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
```